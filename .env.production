# Database Configuration
DATABASE_URL=postgresql://myuser:mypassword@localhost:5432/routine-mail-dev

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Email Configuration (Brevo SMTP)
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=xsmtpsib-55517d93087702de7de878f15086c05742c7a80d511c30c7c03a8fad8ee08f07-RS69d7qDGHCaYTtg
SMTP_FROM=Routine Mail <<EMAIL>>

# OTP Configuration
OTP_EXPIRES_IN_MINUTES=5
OTP_MAX_ATTEMPTS=3
OTP_RESEND_COOLDOWN_MINUTES=3
DAILY_EMAIL_LIMIT=5

# Application Configuration
APP_URL=http://localhost:5173
NODE_ENV=development

# OAuth2 Provider Configuration
OAUTH2_PROVIDER_NAME=office-flow
OAUTH2_CLIENT_ID=routine-mail
OAUTH2_CLIENT_SECRET=routine-mail-secret
OAUTH2_AUTHORIZATION_URL=http://localhost:8092/api/v1/oauth2/pre-authorize
OAUTH2_TOKEN_URL=http://localhost:8092/api/v1/oauth2/token
OAUTH2_USERINFO_URL=http://localhost:8092/api/v1/userinfo
OAUTH2_JWKS_URL=http://localhost:8092/api/v1/.well-known/jwks.json
OAUTH2_REDIRECT_URI=http://localhost:3000/auth/callback
OAUTH2_SCOPES=openid,profile,email,read
OAUTH2_STATE_EXPIRY_MINUTES=20
OAUTH2_ACCESS_TOKEN_EXPIRY_MINUTES=30
OAUTH2_REFRESH_TOKEN_EXPIRY_DAYS=7

# Office Flow Feature Toggle
ENABLE_OFFICE_FLOW=false
