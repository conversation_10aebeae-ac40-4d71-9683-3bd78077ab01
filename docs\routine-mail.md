待办事项系统功能整理
使用 nuxtjs 开发
设计偏工作 商务 不要太多颜色的设计
简约好看的设计

现有待办事项应用的痛点
提醒时间不灵活：
无法设置“每个月的第 X 天”或“30 号的前一天” "最后 x 天"之类的等复杂规则。
现有提醒时间设置缺乏弹性。
通知方式单一：
目前只支持应用内通知（noti），不支持邮件提醒。
新系统开发目标
面向办公人群： 更多地服务于办公用户。
核心功能： 每天会向用户发送电子邮件提醒。
核心差异化功能：邮件提醒
每日自动发送： 每天早上 6:00am 自动发送今日任务邮件。

任务预览： 支持“今天逾期 + 明天任务”预览。

邮件即操作： 支持在邮件中直接打勾完成任务（✅）。

需要可以输入标题 小标题 note 类似 microsoft to do

静默时段： 在非工作时间不发送邮件。

高度可配置的重复提醒
极致灵活的重复提醒设置：
智能日期识别：
每月第 X 天/周 X：例如，“每月第三个星期二”、“每季度最后一个工作日”。
相对日期：例如，“每月 28 号”、“每月倒数第三天”、“季度末前 5 天”。
特定事件前 N 天：例如，“项目截止日期前 3 天”。
条件触发提醒：
基于工作日/非工作日：例如，“每月 15 号，如果是非工作日则顺延至下一个工作日”。可选
基于节假日：避开节假日或在节假日前提醒。
自定义提醒周期： 除了按天、周、月、年，还可以按“每 N 个工作日”、“每 N 周的周二和周四”等。

每月特定日期：
每月的“第 N 个工作日”：例如：每月第 3 个周三、每月最后一个周五。
每月“最后一天”、“倒数第二天”：很多报表场景需要这个。
延期与滞后智能
任务未完成自动提醒： 任务未完成时，自动提醒间隔（1 天后、3 天后再次提醒）。
邮件内嵌操作： 邮件中内嵌“延后一天 / 完成按钮”。

用户可以自己选择
每隔几天提醒
每次提醒多少天的内容
比如 每隔 2 天 提醒接下来 5 天的内容

用https://app.abstractapi.com/api/holidays/tester拿到holiday
