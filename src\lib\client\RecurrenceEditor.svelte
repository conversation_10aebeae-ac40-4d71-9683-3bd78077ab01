<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  import { addMonths, format as formatDateFns } from 'date-fns';
  import { slide } from 'svelte/transition';
  import CustomSelect from './CustomSelect.svelte';
  import flatpickr from 'flatpickr';
  import 'flatpickr/dist/themes/airbnb.css';

  export let rule: any = null;

  const dispatch = createEventDispatcher();

  let endDatePickerWrapper: HTMLDivElement;
  let endDatePicker: flatpickr.Instance | null = null;

  onDestroy(() => {
    endDatePicker?.destroy();
  });

  const DEFAULT_RULE = {
    type: 'daily', // daily, weekly, monthly, yearly
    interval: 1,
    weekdays: [] as number[], // 0=Sun, 1=Mon...
    monthlyType: 'date', // date, weekday, last_day, last_x_day
    monthlyDate: 1, // 1-31
    monthlyLastXDay: 1,
    monthlyWeekNumber: 1, // 1, 2, 3, 4, -1 (last)
    monthlyWeekday: 1, // 0=Sun, 1=Mon...
    yearlyMonth: 1, // 1-12
    endType: 'never', // never, on, after
    endOnDate: '',
    endAfterOccurrences: 10,
  } as const;

  let internalRule = rule ? { ...DEFAULT_RULE, ...rule } : { ...DEFAULT_RULE };

  function setupEndDatePicker() {
    if (!endDatePickerWrapper || endDatePicker) return;
    endDatePicker = flatpickr(endDatePickerWrapper, {
      wrap: true,
      dateFormat: 'Y-m-d',
      altInput: true,
      altFormat: 'M d, Y',
      disableMobile: true,
      defaultDate: internalRule.endOnDate || undefined,
      onChange: (selectedDates) => {
        const d = selectedDates[0];
        if (d) {
          internalRule.endOnDate = formatDateFns(d, 'yyyy-MM-dd');
          updateRule();
        }
      }
    });
  }

  function openEndDatePickerDropdown() {
    // Ensure default is present before opening
    if ((!internalRule.endOnDate || internalRule.endOnDate.trim() === '')) {
      const oneMonthLater = addMonths(new Date(), 1);
      internalRule.endOnDate = formatDateFns(oneMonthLater, 'yyyy-MM-dd');
    }
    setupEndDatePicker();
    // Sync picker with current value
    endDatePicker?.setDate(internalRule.endOnDate, false);
    endDatePicker?.open();
  }

  onMount(() => {
    if (!rule) {
      // Initialize with default rule when no rule is provided
      internalRule = { ...DEFAULT_RULE };
      updateRule();
    }

    // Ensure default for endOnDate is present on mount when endType is 'on'
    if (internalRule.endType === 'on' && (!internalRule.endOnDate || internalRule.endOnDate.trim() === '')) {
      const oneMonthLater = addMonths(new Date(), 1);
      internalRule.endOnDate = formatDateFns(oneMonthLater, 'yyyy-MM-dd');
      updateRule();
    }
  });

  const weekDays = [
    { value: 1, label: 'Monday', short: 'Mon' },
    { value: 2, label: 'Tuesday', short: 'Tue' },
    { value: 3, label: 'Wednesday', short: 'Wed' },
    { value: 4, label: 'Thursday', short: 'Thu' },
    { value: 5, label: 'Friday', short: 'Fri' },
    { value: 6, label: 'Saturday', short: 'Sat' },
    { value: 0, label: 'Sunday', short: 'Sun' }
  ];

  // Initialize picker reactively when wrapper appears and endType is 'on'
  $: if (internalRule.endType === 'on' && endDatePickerWrapper && !endDatePicker) {
    if ((!internalRule.endOnDate || internalRule.endOnDate.trim() === '')) {
      const oneMonthLater = addMonths(new Date(), 1);
      internalRule.endOnDate = formatDateFns(oneMonthLater, 'yyyy-MM-dd');
    }
    setupEndDatePicker();
    endDatePicker?.setDate(internalRule.endOnDate, false);
  }

  // Tear down when toggled off
  $: if (internalRule.endType !== 'on' && endDatePicker) {
    endDatePicker.destroy();
    endDatePicker = null;
  }
  const weekNumbers = [
    { value: 1, label: 'First' }, { value: 2, label: 'Second' }, { value: 3, label: 'Third' },
    { value: 4, label: 'Fourth' }, { value: -1, label: 'Last' }
  ];
  const months = [
      {value: 1, label: 'January'}, {value: 2, label: 'February'}, {value: 3, label: 'March'},
      {value: 4, label: 'April'}, {value: 5, label: 'May'}, {value: 6, label: 'June'},
      {value: 7, label: 'July'}, {value: 8, label: 'August'}, {value: 9, label: 'September'},
      {value: 10, label: 'October'}, {value: 11, label: 'November'}, {value: 12, label: 'December'}
  ];

  // Options for CustomSelect components
  const typeOptions = [
    { value: 'daily', label: 'day(s)' },
    { value: 'weekly', label: 'week(s)' },
    { value: 'monthly', label: 'month(s)' },
    { value: 'yearly', label: 'year(s)' }
  ];

  const weekNumberOptions = weekNumbers.map(wn => ({ value: wn.value.toString(), label: wn.label }));
  const weekDayOptions = weekDays.map(wd => ({ value: wd.value.toString(), label: wd.label }));
  const monthOptions = months.map(m => ({ value: m.value.toString(), label: m.label }));




  function toggleWeekday(day: number) {
    const weekdays = new Set(internalRule.weekdays);
    if (weekdays.has(day)) weekdays.delete(day);
    else weekdays.add(day);
    internalRule.weekdays = Array.from(weekdays).sort((a,b) => a-b);
    updateRule();
  }

  function updateRule() {
    // Basic validation
    if (internalRule.interval < 1) internalRule.interval = 1;
    if (internalRule.endAfterOccurrences < 1) internalRule.endAfterOccurrences = 1;
    if (internalRule.monthlyDate < 1) internalRule.monthlyDate = 1;
    if (internalRule.monthlyDate > 31) internalRule.monthlyDate = 31;
    if (internalRule.monthlyLastXDay < 1) internalRule.monthlyLastXDay = 1;
    if (internalRule.monthlyLastXDay > 31) internalRule.monthlyLastXDay = 31;

    // Default endOnDate when endType === 'on' but date is empty
    if (internalRule.endType === 'on' && (!internalRule.endOnDate || internalRule.endOnDate.trim() === '')) {
      const oneMonthLater = addMonths(new Date(), 1);
      internalRule.endOnDate = formatDateFns(oneMonthLater, 'yyyy-MM-dd');
    }

    // Setup date picker when endType is 'on'
    if (internalRule.endType === 'on') {
      setupEndDatePicker();
    } else {
      endDatePicker?.destroy();
      endDatePicker = null;
    }

    dispatch('change', { ...internalRule });
  }

  function handleTypeChange(event: CustomEvent) {
    internalRule.type = event.detail.value;
    updateRule();
  }

  function handleWeekNumberChange(event: CustomEvent) {
    internalRule.monthlyWeekNumber = parseInt(event.detail.value);
    updateRule();
  }

  function handleWeekdayChange(event: CustomEvent) {
    internalRule.monthlyWeekday = parseInt(event.detail.value);
    updateRule();
  }

  function handleMonthChange(event: CustomEvent) {
    internalRule.yearlyMonth = parseInt(event.detail.value);
    updateRule();
  }

  $: summary = generateSummary(internalRule);

  function generateSummary(r: typeof internalRule | null): string {
    if (!r) return 'No recurrence set.';

    let summary = 'Repeats ';
    if (r.interval > 1) {
        summary += `every ${r.interval} ${r.type}s`;
    } else {
        summary += r.type;
    }

    if (r.type === 'weekly' && r.weekdays.length > 0) {
        const sortedDays = [...r.weekdays].sort();
        const dayNames = sortedDays.map(d => weekDays.find(wd => wd.value === d)?.label).join(', ');
        summary += ` on ${dayNames}`;
    }

    if (r.type === 'monthly') {
        switch(r.monthlyType) {
            case 'date':
                summary += ` on day ${r.monthlyDate}`;
                break;
            case 'weekday':
                const weekNum = weekNumbers.find(wn => wn.value === r.monthlyWeekNumber)?.label.toLowerCase();
                const weekDay = weekDays.find(wd => wd.value === r.monthlyWeekday)?.label;
                summary += ` on the ${weekNum} ${weekDay}`;
                break;
            case 'last_day':
                summary += ' on the last day';
                break;
            case 'last_x_day':
                summary += ` on the ${r.monthlyLastXDay === 1 ? 'last' : `${r.monthlyLastXDay}th to last`} day`;
                break;
        }
    }

    if (r.type === 'yearly') {
        const monthName = months.find(m => m.value === r.yearlyMonth)?.label;
        summary += ` on ${monthName} ${r.monthlyDate}`;
    }

    if (r.endType === 'on' && r.endOnDate) summary += `, until ${r.endOnDate}`;
    if (r.endType === 'after') summary += `, for ${r.endAfterOccurrences} occurrences`;

    return summary + '.';
  }

</script>

<div class="recurrence-editor">
  <div class="recurrence-settings" transition:slide>
      <div class="setting-row">
        <label for="interval">Repeat every</label>
        <div class="input-row">
          <input type="number" id="interval" min="1" class="interval-input" bind:value={internalRule.interval} on:change={updateRule}>
          <CustomSelect
            value={internalRule.type}
            options={typeOptions}
            size="small"
            on:change={handleTypeChange}
          />
        </div>
      </div>

      {#if internalRule.type === 'weekly'}
        <div class="setting-row weekdays">
          <label>On</label>
          <div class="weekday-selector">
            {#each weekDays as day}
              <button type="button" class="weekday-btn" class:active={internalRule.weekdays.includes(day.value)} on:click={() => toggleWeekday(day.value)}>
                <span class="hidden md:inline">{day.label}</span>
                <span class="md:hidden">{day.short}</span>
              </button>
            {/each}
          </div>
        </div>
      {/if}

      {#if internalRule.type === 'monthly'}
        <div class="monthly-options">
            <div class="radio-group">
              <input type="radio" id="monthly-date" value="date" bind:group={internalRule.monthlyType} on:change={updateRule}>
              <label for="monthly-date">On day</label>
              <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyDate} disabled={internalRule.monthlyType !== 'date'} on:change={updateRule}>
            </div>
            <div class="radio-group">
              <div class="radio-row">
                <input type="radio" id="monthly-weekday" value="weekday" bind:group={internalRule.monthlyType} on:change={updateRule}>
                <label for="monthly-weekday">On the</label>
              </div>
              <div class="select-row">
                <CustomSelect
                  value={internalRule.monthlyWeekNumber.toString()}
                  options={weekNumberOptions}
                  size="small"
                  disabled={internalRule.monthlyType !== 'weekday'}
                  on:change={handleWeekNumberChange}
                />
                <CustomSelect
                  value={internalRule.monthlyWeekday.toString()}
                  options={weekDayOptions}
                  size="small"
                  disabled={internalRule.monthlyType !== 'weekday'}
                  on:change={handleWeekdayChange}
                />
              </div>
            </div>
             <div class="radio-group">
              <input type="radio" id="monthly-lastday" value="last_day" bind:group={internalRule.monthlyType} on:change={updateRule}>
              <label for="monthly-lastday">On the last day of the month</label>
            </div>
             <div class="radio-group">
              <input type="radio" id="monthly-last-x-day" value="last_x_day" bind:group={internalRule.monthlyType} on:change={updateRule}>
               <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyLastXDay} disabled={internalRule.monthlyType !== 'last_x_day'} on:change={updateRule}>
              <label for="monthly-last-x-day">day(s) before the end of the month</label>
            </div>
        </div>
      {/if}

      {#if internalRule.type === 'yearly'}
        <div class="setting-row yearly-row">
            <label>On</label>
            <div class="yearly-inputs">
              <CustomSelect
                value={internalRule.yearlyMonth.toString()}
                options={monthOptions}
                size="small"
                on:change={handleMonthChange}
              />
              <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyDate} on:change={updateRule}>
            </div>
        </div>
      {/if}

      <div class="setting-row end-condition mobile-compact">
          <label>Ends</label>
          <div class="end-options">
               <div class="radio-group end-radio-group">
                  <input type="radio" id="end-never" value="never" bind:group={internalRule.endType} on:change={updateRule}>
                  <label for="end-never">Never</label>
              </div>
               <div class="radio-group end-radio-group">
                  <input type="radio" id="end-on" value="on" bind:group={internalRule.endType} on:change={updateRule}>
                  <!-- Auto-ensure default one month later when toggled to 'On' -->
                  {#if internalRule.endType === 'on' && (!internalRule.endOnDate || internalRule.endOnDate.trim() === '')}
                    {@html (() => { internalRule.endOnDate = formatDateFns(addMonths(new Date(), 1), 'yyyy-MM-dd'); '' })()}
                  {/if}

                  <label for="end-on">On</label>
                  {#if internalRule.endType === 'on'}
                    <div class="custom-date-input" bind:this={endDatePickerWrapper}>
                      <input
                        type="text"
                        bind:value={internalRule.endOnDate}
                        class="date-input"
                        placeholder="Select Date..."
                        data-input
                      />
                      <div class="input-icon" role="button" tabindex="0" aria-label="Open end date picker" on:click|stopPropagation={openEndDatePickerDropdown} on:keydown={(e) => (e.key === 'Enter' || e.key === ' ') && (e.preventDefault(), openEndDatePickerDropdown())}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                      </div>
                    </div>
                  {/if}
              </div>
               <div class="radio-group end-radio-group">
                  <input type="radio" id="end-after" value="after" bind:group={internalRule.endType} on:change={updateRule}>
                  <label for="end-after">After</label>
                  <input type="number" min="1" class="occurrences-input" bind:value={internalRule.endAfterOccurrences} disabled={internalRule.endType !== 'after'} on:change={updateRule}>
                  <span class="occurrences-text">occurrences</span>
              </div>
          </div>
      </div>
  </div>

  <!-- Desktop Summary -->
  {#if internalRule}
    <div class="summary hidden md:block">
      <strong>Summary:</strong> {summary}
    </div>
  {/if}
</div>

<style>
  .recurrence-editor {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    font-size: 0.875rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  }

  .recurrence-settings {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  .setting-row, .monthly-options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.8);
    padding: 1.25rem;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
  }

  .setting-row:hover, .monthly-options:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
  }

  .monthly-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.25rem;
  }

  .setting-row label {
    font-weight: 600;
    color: #374151;
    margin-right: 0.75rem;
    font-size: 0.875rem;
    min-width: fit-content;
  }
  input[type="number"], select, input[type="date"] {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    appearance: none;
    cursor: pointer;
  }

  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
  }

  input[type="number"]:focus, select:focus, input[type="date"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  .interval-input, .day-input, .occurrences-input {
    width: 90px;
  }
  .weekday-selector {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .weekday-btn {
    min-width: 50px;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    cursor: pointer;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    font-size: 0.8rem;
  }

  .weekday-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  .weekday-btn.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  }
  .radio-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    background: rgba(248, 250, 252, 0.7);
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    width: 100%;
  }

  .radio-group:hover {
    background: rgba(248, 250, 252, 0.9);
    border-color: #d1d5db;
  }

  .radio-group label {
    font-weight: 500;
    color: #374151;
    cursor: pointer;
  }

  .radio-group input[type="radio"] {
    margin: 0;
    transform: scale(1.1);
  }

  .summary {
    margin-top: 1.5rem;
    padding: 1.25rem 1.5rem;
    color: #374151;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(29, 78, 216, 0.06));
    border-radius: 16px;
    border: 1px solid rgba(59, 130, 246, 0.15);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  .summary strong {
    color: #1f2937;
    font-weight: 600;
  }

  /* Yearly row specific styles */
  .yearly-row {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .yearly-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  /* End condition specific styles */
  .end-radio-group {
    display: flex;
    align-items: center;
    min-height: 48px;
  }

  .end-radio-group input[type="radio"] {
    flex-shrink: 0;
    margin: 0;
    margin-right: 0.5rem;
  }

  .end-radio-group label {
    flex-shrink: 0;
    white-space: nowrap;
    margin: 0;
    display: flex;
    align-items: center;
  }

  .end-radio-group input[type="date"],
  .end-radio-group input[type="number"] {
    flex-shrink: 1;
    margin-left: 0.5rem;
  }

  .occurrences-text {
    flex-shrink: 0;
    white-space: nowrap;
    font-weight: 500;
    color: #374151;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
  }

  .custom-date-input .input-icon {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    cursor: pointer;
    border-radius: 4px;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
  }

  .custom-date-input:focus-within .input-icon,
  .custom-date-input .input-icon:hover {
    color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
  }

  .custom-date-input {
    position: relative;
    display: inline-block;
    margin-left: 0.5rem;
  }

  .custom-date-input .date-input {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    min-width: 120px;
  }

  .custom-date-input .date-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Mobile responsive styles */

	  /* Desktop spacing for end options */
	  .end-options {
	    display: flex;
	    flex-direction: column;
	    gap: 0.75rem;
	    width: 100%;
	  }

  @media (max-width: 768px) {
    .recurrence-editor {
      padding: 0;
      font-size: 0.8rem;
      background: transparent;
      border: none;
      box-shadow: none;
    }

    .recurrence-settings {
      gap: 1rem;
    }

    .setting-row, .monthly-options {
      padding: 0;
      gap: 0.75rem;
      background: transparent;
      border: none;
      box-shadow: none;
      border-radius: 0;
    }

    /* Ends section 在移动端不再用负 margin，避免截断 */
    .setting-row.end-condition.mobile-compact {
      margin-bottom: 0.5rem;
      padding-bottom: 0.25rem;
    }

    .setting-row label {
      font-size: 0.8rem;
      margin-right: 0;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }

    /* First row: Repeat every X type */
    .setting-row:first-child {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .setting-row:first-child label {
      margin-bottom: 0.25rem;
      white-space: nowrap;
    }

    /* Input row for interval and type */
    .setting-row:first-child .input-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
    }

    input[type="number"], select, input[type="date"] {
      padding: 0.4rem 0.6rem;
      font-size: 0.75rem;
      border-radius: 4px;
      border: 1px solid #d1d5db;
    }

    .interval-input {
      width: 60px;
      max-width: 60px;
    }

    .day-input, .occurrences-input {
      width: 70px;
      max-width: 70px;
    }

    /* Weekday selector optimization */
    .weekdays {
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 0.75rem;
    }

    .weekdays label {
      margin-bottom: 0.5rem;
    }

    .weekday-selector {
      gap: 0.3rem;
      width: 100%;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
    }

    .weekday-btn {
      padding: 0.5rem 0.3rem;
      font-size: 0.7rem;
      border-radius: 6px;
      min-width: 0;
      text-align: center;
      border: 1px solid #d1d5db;
      background: white;
      transition: none;
      box-shadow: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .weekday-btn:hover {
      box-shadow: none;
      transform: none;
    }

    /* Remove hover shadows on mobile */
    .setting-row:hover, .monthly-options:hover {
      box-shadow: none;
      border-color: #e5e7eb;
    }

    .radio-group:hover {
      background: transparent;
      border-color: #e5e7eb;
    }

    .weekday-btn.active {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
    }

    .monthly-options {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .radio-group {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
      flex-wrap: wrap;
      padding: 0.5rem 0.75rem;
      min-height: 40px;
    }

    .radio-group label {
      font-size: 0.7rem;
      margin: 0;
      padding: 0;
      line-height: 16px;
      height: 16px;
      display: flex;
      align-items: center;
    }

    .radio-group input[type="radio"] {
      margin: 0;
      padding: 0;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      position: relative;
      top: 0;
    }

    .radio-group input[type="number"], .radio-group input[type="date"] {
      height: 24px;
      line-height: 16px;
      padding: 4px 8px;
      margin: 0;
    }

    .radio-group span {
      line-height: 16px;
      height: 16px;
      display: flex;
      align-items: center;
    }

    /* Special layout for monthly weekday option */
    .radio-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .select-row {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-left: 1.5rem;
      flex-wrap: wrap;
    }

    .end-options {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-bottom: 0;
    }

    .end-options label {
      margin-bottom: 0.5rem;
    }

    .end-condition {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      margin-bottom: 0;
    }

    .end-condition > label {
      margin-bottom: 0.25rem;
      font-size: 0.8rem;
      font-weight: 500;
      color: #374151;
    }

    .end-condition input[type="radio"] {
      margin-right: 0.5rem;
    }

    /* Mobile yearly row styles */
    .yearly-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .yearly-row label {
      margin-bottom: 0.25rem;
    }

    .yearly-inputs {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    /* Mobile end radio group styles */
    .end-radio-group {
      min-height: 40px;
      width: 100%;
      padding: 0.5rem 0.75rem;
      background: rgba(248, 250, 252, 0.7);
      border-radius: 12px;
      border: 1px solid #e5e7eb;
      display: flex;
      align-items: center;
    }

    .end-radio-group input[type="radio"] {
      width: 16px;
      height: 16px;
      margin: 0;
      margin-right: 0.5rem;
      position: relative;
      top: 0;
      flex-shrink: 0;
    }

    .end-radio-group label {
      font-size: 0.7rem;
      line-height: 1;
      margin: 0;
      margin-right: 0.5rem;
      display: flex;
      align-items: center;
      height: auto;
      flex-shrink: 0;
    }

    .end-radio-group input[type="date"],
    .end-radio-group input[type="number"] {
      height: 28px;
      padding: 4px 8px;
      margin: 0;
      margin-left: 0.25rem;
      font-size: 0.75rem;
    }

    .custom-date-input {
      margin-left: 0.25rem;
      flex: 1;
      min-width: 0;
      position: relative;
    }

    .custom-date-input .date-input {
      height: 28px;
      padding: 4px 32px 4px 8px; /* 预留右侧icon空间 */
      font-size: 0.75rem;
      border-radius: 8px;
      min-width: 100px;
      width: 100%;
    }

    .occurrences-text {
      font-size: 0.7rem;
      line-height: 1;
      margin-left: 0.25rem;
      display: flex;
      align-items: center;
      height: auto;
    }

    .summary {
      padding: 0.75rem;
      font-size: 0.8rem;
      margin-top: 1rem;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
    }
  }
</style>