<script lang="ts">
  import LoadingSpinner from './LoadingSpinner.svelte';
  import { createEventDispatcher } from 'svelte';

  export let variant: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success' = 'primary';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let disabled = false;
  export let loading = false;
  export let href: string | undefined = undefined;
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let loadingText: string = '';

  const dispatch = createEventDispatcher();

  $: isDisabled = disabled || loading;

  $: classes = [
    'btn',
    `btn-${variant}`,
    size !== 'md' ? `btn-${size}` : '',
    loading ? 'cursor-wait' : '',
    isDisabled ? 'opacity-75' : '',
    $$props.class || ''
  ].filter(Boolean).join(' ');

  $: spinnerSize = size === 'sm' ? 'sm' : size === 'lg' ? 'md' : 'sm';
  $: spinnerColor = variant === 'primary' || variant === 'danger' || variant === 'success' ? 'white' : 'primary';
</script>

{#if href}
  <a {href} class={classes} class:pointer-events-none={isDisabled} on:click {...$$restProps}>
    <div class="flex items-center justify-center gap-2 flex-nowrap whitespace-nowrap">
      {#if loading}
        <LoadingSpinner size={spinnerSize} color={spinnerColor} />
      {/if}
      <span class="inline-flex items-center whitespace-nowrap" class:opacity-0={loading && loadingText}>
        <slot />
      </span>
      {#if loading && loadingText}
        <span class="absolute">{loadingText}</span>
      {/if}
    </div>
  </a>
{:else}
  <button
    {type}
    disabled={isDisabled}
    class={classes}
    class:pointer-events-none={loading}
    on:click
    {...$$restProps}
  >
    <div class="flex items-center justify-center gap-2 relative flex-nowrap whitespace-nowrap">
      {#if loading}
        <LoadingSpinner size={spinnerSize} color={spinnerColor} />
      {/if}
      <span class="inline-flex items-center whitespace-nowrap" class:opacity-0={loading && loadingText}>
        <slot />
      </span>
      {#if loading && loadingText}
        <span class="absolute">{loadingText}</span>
      {/if}
    </div>
  </button>
{/if}
