<script lang="ts">
  export let href: string;
  export let ariaLabel: string = 'Add new item';
</script>

<a
  {href}
  class="floating-action-btn"
  aria-label={ariaLabel}
>
  <div class="btn-content">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
    </svg>
  </div>
</a>

<style>
  .floating-action-btn {
    @apply md:hidden fixed bottom-24 right-4 z-40 w-16 h-16 rounded-full flex items-center justify-center;
    @apply transition-all duration-300 ease-out;
    @apply focus:outline-none focus:ring-4 focus:ring-slate-200/50;

    /* Modern glassmorphism effect */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.3);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);

    /* Subtle pulse animation */
    animation: subtle-pulse 3s ease-in-out infinite;
  }

  .floating-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .floating-action-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 1px 4px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .btn-content {
    @apply text-slate-700 transition-all duration-300;
    transform: translateZ(0);
  }

  .floating-action-btn:hover .btn-content {
    @apply text-slate-800;
    transform: rotate(90deg);
  }

  @keyframes subtle-pulse {
    0%, 100% {
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 0 0 0 rgba(148, 163, 184, 0);
    }
    50% {
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 0 0 8px rgba(148, 163, 184, 0.1);
    }
  }

  /* Ensure smooth performance */
  .floating-action-btn {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
  }
</style>
