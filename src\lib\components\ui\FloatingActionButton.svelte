<script lang="ts">
  export let href: string;
  export let ariaLabel: string = 'Add new item';
</script>

<a
  {href}
  class="floating-action-btn"
  aria-label={ariaLabel}
>
  <div class="btn-content">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
    </svg>
  </div>
</a>

<style>
  .floating-action-btn {
    @apply md:hidden fixed bottom-24 right-4 z-40 w-16 h-16 rounded-full flex items-center justify-center;
    @apply transition-all duration-300 ease-out;
    @apply focus:outline-none focus:ring-4 focus:ring-slate-300/30;

    /* Modern glassmorphism effect with slate accent */
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.98));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow:
      0 8px 32px rgba(71, 85, 105, 0.15),
      0 2px 8px rgba(71, 85, 105, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);

    /* Subtle pulse animation */
    animation: subtle-pulse 3s ease-in-out infinite;
  }

  .floating-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    background: linear-gradient(135deg, rgba(241, 245, 249, 0.98), rgba(226, 232, 240, 0.95));
    box-shadow:
      0 12px 40px rgba(71, 85, 105, 0.2),
      0 4px 12px rgba(71, 85, 105, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.95);
  }

  .floating-action-btn:active {
    transform: translateY(0) scale(0.98);
    background: linear-gradient(135deg, rgba(226, 232, 240, 0.95), rgba(203, 213, 225, 0.98));
    box-shadow:
      0 4px 16px rgba(71, 85, 105, 0.15),
      0 1px 4px rgba(71, 85, 105, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .btn-content {
    @apply text-slate-600 transition-all duration-300;
    transform: translateZ(0);
  }

  .floating-action-btn:hover .btn-content {
    @apply text-slate-700;
    transform: rotate(90deg);
  }

  @keyframes subtle-pulse {
    0%, 100% {
      box-shadow:
        0 8px 32px rgba(71, 85, 105, 0.15),
        0 2px 8px rgba(71, 85, 105, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 0 0 0 rgba(148, 163, 184, 0);
    }
    50% {
      box-shadow:
        0 8px 32px rgba(71, 85, 105, 0.15),
        0 2px 8px rgba(71, 85, 105, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 0 0 8px rgba(148, 163, 184, 0.15);
    }
  }

  /* Ensure smooth performance */
  .floating-action-btn {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
  }
</style>
