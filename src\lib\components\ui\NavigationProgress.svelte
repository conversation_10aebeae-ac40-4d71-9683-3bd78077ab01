<script lang="ts">
  import { navigating } from '$app/stores';
  import { onMount } from 'svelte';
  
  let progress = 0;
  let visible = false;
  let progressInterval: number;
  
  $: if ($navigating) {
    startProgress();
  } else {
    completeProgress();
  }
  
  function startProgress() {
    visible = true;
    progress = 0;
    
    // Simulate progress
    progressInterval = setInterval(() => {
      if (progress < 90) {
        progress += Math.random() * 10;
      }
    }, 100);
  }
  
  function completeProgress() {
    if (progressInterval) {
      clearInterval(progressInterval);
    }
    
    if (visible) {
      progress = 100;
      setTimeout(() => {
        visible = false;
        progress = 0;
      }, 200);
    }
  }
  
  onMount(() => {
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  });
</script>

{#if visible}
  <div class="fixed top-0 left-0 right-0 z-50 h-1 bg-gray-200">
    <div 
      class="h-full bg-blue-600 transition-all duration-200 ease-out"
      style="width: {progress}%"
    ></div>
  </div>
{/if}
