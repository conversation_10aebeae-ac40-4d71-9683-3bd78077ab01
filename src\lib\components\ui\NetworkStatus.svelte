<script lang="ts">
  import { onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  
  let isOnline = true;
  let showOfflineMessage = false;
  
  onMount(() => {
    // Check initial status
    isOnline = navigator.onLine;
    
    // Listen for online/offline events
    const handleOnline = () => {
      isOnline = true;
      showOfflineMessage = false;
    };
    
    const handleOffline = () => {
      isOnline = false;
      showOfflineMessage = true;
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  });
</script>

{#if showOfflineMessage}
  <div 
    class="fixed top-16 left-4 right-4 z-40 bg-red-50 border border-red-200 rounded-lg p-3 shadow-lg"
    transition:fade={{ duration: 300 }}
  >
    <div class="flex items-center gap-2">
      <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <div>
        <p class="text-sm font-medium text-red-800">You're offline</p>
        <p class="text-xs text-red-600">Some features may not work until you reconnect.</p>
      </div>
    </div>
  </div>
{/if}
