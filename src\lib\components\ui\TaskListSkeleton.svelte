<script lang="ts">
  import Skeleton from './Skeleton.svelte';
  
  export let count: number = 5;
  export let compact: boolean = false;
</script>

<div class="space-y-3">
  {#each Array(count) as _, i}
    <div class="card p-4 {compact ? 'md:hidden' : 'hidden md:block'}">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3 flex-1">
          <!-- Checkbox skeleton -->
          <Skeleton width="1.25rem" height="1.25rem" />
          
          <div class="flex-1 space-y-2">
            <!-- Title skeleton -->
            <Skeleton width={`${60 + Math.random() * 30}%`} height="1.25rem" />
            
            <!-- Description skeleton -->
            <Skeleton width={`${40 + Math.random() * 40}%`} height="1rem" />
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <!-- Badge skeleton -->
          <Skeleton width="4rem" height="1.5rem" />
          
          <!-- Actions skeleton -->
          <Skeleton width="2rem" height="2rem" />
        </div>
      </div>
    </div>
    
    <!-- Mobile card skeleton -->
    <div class="card p-4 md:hidden">
      <div class="space-y-3">
        <div class="flex items-start gap-3">
          <Skeleton width="1.25rem" height="1.25rem" />
          <div class="flex-1 space-y-2">
            <Skeleton width="80%" height="1.25rem" />
            <Skeleton width="60%" height="1rem" />
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <Skeleton width="3rem" height="1.25rem" />
          <Skeleton width="5rem" height="1.25rem" />
        </div>
      </div>
    </div>
  {/each}
</div>
