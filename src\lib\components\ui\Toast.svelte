<script lang="ts">
  import { fly, fade } from 'svelte/transition';
  import { toasts, type Toast } from '$lib/stores/toast';
  
  export let toast: Toast;
  
  $: typeConfig = {
    success: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-800',
      iconColor: 'text-green-400',
      icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
    },
    error: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200', 
      textColor: 'text-red-800',
      iconColor: 'text-red-400',
      icon: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
    },
    warning: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-800', 
      iconColor: 'text-yellow-400',
      icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z'
    },
    info: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      iconColor: 'text-blue-400', 
      icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    }
  };
  
  $: config = typeConfig[toast.type];
  
  function dismiss() {
    toasts.remove(toast.id);
  }
</script>

<div
  class="max-w-sm w-full {config.bgColor} {config.borderColor} border rounded-lg shadow-lg pointer-events-auto"
  in:fly={{ y: -50, duration: 300 }}
  out:fade={{ duration: 200 }}
>
  <div class="p-4">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 {config.iconColor}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={config.icon} />
        </svg>
      </div>
      
      <div class="ml-3 w-0 flex-1">
        <p class="text-sm font-medium {config.textColor}">
          {toast.title}
        </p>
        {#if toast.message}
          <p class="mt-1 text-sm {config.textColor} opacity-90">
            {toast.message}
          </p>
        {/if}
      </div>
      
      {#if toast.dismissible}
        <div class="ml-4 flex-shrink-0 flex">
          <button
            class="inline-flex {config.textColor} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-md"
            on:click={dismiss}
          >
            <span class="sr-only">Close</span>
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      {/if}
    </div>
  </div>
</div>
