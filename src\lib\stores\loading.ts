import { writable } from 'svelte/store';

interface LoadingState {
  [key: string]: boolean;
}

function createLoadingStore() {
  const { subscribe, update } = writable<LoadingState>({});

  return {
    subscribe,
    start: (key: string) => {
      update(state => ({ ...state, [key]: true }));
    },
    stop: (key: string) => {
      update(state => {
        const newState = { ...state };
        delete newState[key];
        return newState;
      });
    },
    isLoading: (key: string) => {
      let isLoading = false;
      subscribe(state => {
        isLoading = state[key] || false;
      })();
      return isLoading;
    },
    clear: () => {
      update(() => ({}));
    }
  };
}

export const loading = createLoadingStore();

// Helper function for async operations
export async function withLoading<T>(
  key: string, 
  operation: () => Promise<T>
): Promise<T> {
  loading.start(key);
  try {
    const result = await operation();
    return result;
  } finally {
    loading.stop(key);
  }
}
