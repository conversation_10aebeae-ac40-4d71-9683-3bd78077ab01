import { writable } from 'svelte/store';

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  dismissible?: boolean;
}

function createToastStore() {
  const { subscribe, update } = writable<Toast[]>([]);

  return {
    subscribe,
    add: (toast: Omit<Toast, 'id'>) => {
      const id = Math.random().toString(36).substr(2, 9);
      const newToast: Toast = {
        id,
        duration: 5000,
        dismissible: true,
        ...toast
      };

      update(toasts => [...toasts, newToast]);

      // Auto remove after duration
      if (newToast.duration && newToast.duration > 0) {
        setTimeout(() => {
          update(toasts => toasts.filter(t => t.id !== id));
        }, newToast.duration);
      }

      return id;
    },
    remove: (id: string) => {
      update(toasts => toasts.filter(t => t.id !== id));
    },
    clear: () => {
      update(() => []);
    },
    success: (title: string, message?: string) => {
      return createToastStore().add({ type: 'success', title, message });
    },
    error: (title: string, message?: string) => {
      return createToastStore().add({ type: 'error', title, message, duration: 7000 });
    },
    warning: (title: string, message?: string) => {
      return createToastStore().add({ type: 'warning', title, message });
    },
    info: (title: string, message?: string) => {
      return createToastStore().add({ type: 'info', title, message });
    }
  };
}

export const toasts = createToastStore();
