import { format } from 'date-fns';

export const DEFAULT_TZ = 'Asia/Kuala_Lumpur';

// Timezone offset helpers (simplified approach)
const TIMEZONE_OFFSETS: Record<string, number> = {
  'Asia/Kuala_Lumpur': 8,
  'Asia/Singapore': 8,
  'Asia/Jakarta': 7,
  'Asia/Bangkok': 7,
  'Asia/Manila': 8,
  'Asia/Hong_Kong': 8,
  'Asia/Shanghai': 8,
  'Asia/Tokyo': 9,
  'UTC': 0,
  'America/New_York': -5, // EST (simplified, doesn't handle DST)
  'America/Los_Angeles': -8, // PST (simplified)
  'Europe/London': 0 // GMT (simplified)
};

// Combine local date/time in a specific timezone into a UTC ISO string
export function toUTCISO(dateStr: string | null | undefined, timeStr: string | null | undefined, tz: string = DEFAULT_TZ): string | null {
  if (!dateStr) return null;
  const time = (timeStr && timeStr.trim()) ? timeStr : '09:00';
  const localISO = `${dateStr}T${time}:00`;
  const localDate = new Date(localISO);

  // Apply timezone offset to get UTC
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const utcDate = new Date(localDate.getTime() - (offsetHours * 60 * 60 * 1000));
  return utcDate.toISOString();
}

// Convert a UTC ISO string to local date/time parts in a timezone
export function fromUTCToParts(utcISO: string, tz: string = DEFAULT_TZ): { date: string; time: string } {
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  return {
    date: format(localDate, 'yyyy-MM-dd'),
    time: format(localDate, 'HH:mm')
  };
}

// Human friendly format for display in a timezone
export function formatDisplay(utcISO: string | null | undefined, tz: string = DEFAULT_TZ): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));
  return format(localDate, 'PPpp'); // e.g., Aug 10, 2025 at 9:00 AM
}

// Helpers to compare with today/tomorrow/yesterday in a timezone
export function relativeLabel(utcISO: string | null | undefined, tz: string = DEFAULT_TZ): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  const now = new Date();
  const todayLocal = new Date(now.getTime() + (offsetHours * 60 * 60 * 1000));

  const startOf = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());
  const day = startOf(localDate);
  const today = startOf(todayLocal);

  const diffDays = Math.round((day.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  const timePart = format(localDate, 'p');

  if (diffDays === 0) return `Today at ${timePart}`;
  if (diffDays === 1) return `Tomorrow at ${timePart}`;
  if (diffDays === -1) return `Yesterday at ${timePart}`;
  if (diffDays > 0) return `${format(localDate, 'P')} at ${timePart}`;
  return `${Math.abs(diffDays)} days overdue (${format(localDate, 'P')} at ${timePart})`;
}

