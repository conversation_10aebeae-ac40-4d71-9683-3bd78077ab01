import { json, redirect } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { oauth2Service } from '$lib/server/oauth2/OAuth2Service.js';
import { consumeState, storePendingOfficeFlowUser } from '$lib/server/oauth2/state';
import {
  getUserByOfficeFlowUserId,
  createUser,
  createOfficeFlowLink,
  updateOfficeFlowLinkTokens,
  createSession
} from '$lib/server/db/operations.js';
import { generateJWT } from '$lib/server/auth.js';

/**
 * Handle OAuth2 callback from Office Flow
 * Processes authorization code and creates/links user accounts
 */
export const GET: RequestHandler = async ({ url, cookies }) => {
  try {
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    // Handle OAuth2 errors
    if (error) {
      console.error('OAuth2 error:', error);
      return redirect(302, '/login?error=oauth2_error');
    }

    // Validate required parameters
    if (!code || !state) {
      console.error('Missing code or state parameter');
      return redirect(302, '/login?error=invalid_request');
    }

    // 验证并消费state参数
    const stateData = await consumeState(state);
    if (!stateData) {
      console.error('[OAuth2Service] Invalid or expired state parameter');
      return redirect(302, '/login?error=invalid_state');
    }

    // 使用OAuth2Service交换授权码获取token
    const token = await oauth2Service.exchangeCodeForTokens(code, stateData.codeVerifier);

    // 使用OAuth2Service获取用户信息
    const officeFlowUser = await oauth2Service.getUserInfo(token.accessToken);

    // Check if this Office Flow user is already linked to a local account
    let user = await getUserByOfficeFlowUserId(officeFlowUser.id);

    if (stateData.isLinking && stateData.userId) {
      // This is an account linking request
      if (user) {
        // Office Flow account is already linked to another user
        return redirect(302, '/dashboard/settings?error=account_already_linked');
      }

      // Link Office Flow account to existing user
      await createOfficeFlowLink({
        userId: stateData.userId,
        officeFlowUserId: officeFlowUser.id,
        officeFlowEmail: officeFlowUser.email,
        officeFlowName: officeFlowUser.name,
        officeFlowAvatar: officeFlowUser.avatar,
        officeFlowDepartment: officeFlowUser.department,
        officeFlowPosition: officeFlowUser.position,
        accessToken: token.accessToken,
        refreshToken: token.refreshToken,
        tokenExpiresAt: token.expiresAt
      });

      return redirect(302, '/dashboard/settings?success=account_linked');
    } else {
      // This is a login request
      if (!user) {
        // Office Flow user is not linked to any Routine Mail account
        // Store pending user info and redirect to registration page
        const pendingState = storePendingOfficeFlowUser(
          {
            id: officeFlowUser.id,
            email: officeFlowUser.email,
            name: officeFlowUser.name,
            avatar: officeFlowUser.avatar,
            department: officeFlowUser.department,
            position: officeFlowUser.position
          },
          {
            accessToken: token.accessToken,
            refreshToken: token.refreshToken,
            expiresAt: token.expiresAt
          }
        );

        // Redirect to registration page with pending state
        return redirect(302, `/register?pending_office_flow=${pendingState}`);
      } else {
        // Update existing Office Flow link tokens
        await updateOfficeFlowLinkTokens(
          user.id,
          token.accessToken,
          token.refreshToken,
          token.expiresAt
        );
      }

      // Create JWT session for the user
      const jwtPayload = { userId: user.id, email: user.email };
      const jwtToken = generateJWT(jwtPayload);
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      await createSession({
        userId: user.id,
        token: jwtToken,
        expiresAt
      });

      // Set authentication cookie
      cookies.set('auth-token', jwtToken, {
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 // 7 days
      });

      // Redirect to dashboard or specified redirect URL
      const redirectTo = stateData.redirectTo || '/dashboard';
      return redirect(302, redirectTo);
    }

  } catch (error) {
    // Check if this is a SvelteKit redirect (which is expected)
    if (error && typeof error === 'object' && 'status' in error && error.status === 302) {
      // This is a normal redirect, re-throw it
      throw error;
    }

    console.error('OAuth2 callback error:', error);
    return redirect(302, '/login?error=oauth2_callback_failed');
  }
};

/**
 * Handle POST requests for programmatic OAuth2 callback processing
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const { code, state } = await request.json();

    if (!code || !state) {
      return json({ error: 'Missing code or state parameter' }, { status: 400 });
    }

    // Exchange authorization code for access token
    const { token, stateData } = await exchangeCodeForToken(code, state);

    // Get user information from Office Flow
    const officeFlowUser = await getUserInfo(token.access_token);

    // Check if this Office Flow user is already linked to a local account
    let user = await getUserByOfficeFlowUserId(officeFlowUser.id);

    if (stateData.isLinking && stateData.userId) {
      // Account linking request
      if (user) {
        return json({ error: 'Office Flow account is already linked to another user' }, { status: 409 });
      }

      await createOfficeFlowLink({
        userId: stateData.userId,
        officeFlowUserId: officeFlowUser.id,
        officeFlowEmail: officeFlowUser.email,
        officeFlowName: officeFlowUser.name,
        officeFlowAvatar: officeFlowUser.avatar,
        officeFlowDepartment: officeFlowUser.department,
        officeFlowPosition: officeFlowUser.position,
        accessToken: token.access_token,
        refreshToken: token.refresh_token,
        tokenExpiresAt: new Date(Date.now() + token.expires_in * 1000)
      });

      return json({ message: 'Account linked successfully' });
    } else {
      // Login request
      if (!user) {
        // Office Flow user is not linked to any Routine Mail account
        // For POST requests, return error instead of redirect
        return json({
          error: 'Office Flow account not linked',
          message: 'This Office Flow account is not linked to any Routine Mail account. Please register first and then link your accounts in settings.',
          requiresRegistration: true
        }, { status: 400 });
      } else {
        await updateOfficeFlowLinkTokens(
          user.id,
          token.access_token,
          token.refresh_token,
          new Date(Date.now() + token.expires_in * 1000)
        );
      }

      // Create JWT session
      const jwtPayload = { userId: user.id, email: user.email };
      const jwtToken = generateJWT(jwtPayload);
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      await createSession({
        userId: user.id,
        token: jwtToken,
        expiresAt
      });

      // Set cookie
      cookies.set('auth-token', jwtToken, {
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60
      });

      return json({
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          isVerified: user.isVerified
        }
      });
    }

  } catch (error) {
    console.error('OAuth2 callback error:', error);
    return json({ error: 'OAuth2 callback failed' }, { status: 500 });
  }
};
