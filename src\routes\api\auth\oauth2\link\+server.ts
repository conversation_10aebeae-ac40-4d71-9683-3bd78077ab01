import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { generateAuthorizationUrl } from '$lib/server/oauth2/client.js';
import { 
  getOfficeFlowLinkByUserId, 
  deactivateOfficeFlowLink 
} from '$lib/server/db/operations.js';

/**
 * Generate OAuth2 authorization URL for account linking
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }
    
    const { redirectTo } = await request.json();
    
    // Check if user already has an Office Flow link
    const existingLink = await getOfficeFlowLinkByUserId(locals.user.id);
    if (existingLink) {
      return json({ error: 'Office Flow account is already linked' }, { status: 409 });
    }
    
    // Generate authorization URL for linking
    const { url } = generateAuthorizationUrl({
      redirectTo: redirectTo || '/dashboard/settings',
      isLinking: true,
      userId: locals.user.id
    });
    
    return json({ authorizationUrl: url });
    
  } catch (error) {
    console.error('OAuth2 link generation error:', error);
    return json({ error: 'Failed to generate authorization URL' }, { status: 500 });
  }
};

/**
 * Get current Office Flow link status
 */
export const GET: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Get current Office Flow link
    const link = await getOfficeFlowLinkByUserId(locals.user.id);
    
    if (link) {
      return json({
        isLinked: true,
        officeFlowUser: {
          id: link.officeFlowUserId,
          email: link.officeFlowEmail,
          name: link.officeFlowName,
          avatar: link.officeFlowAvatar,
          department: link.officeFlowDepartment,
          position: link.officeFlowPosition
        },
        linkedAt: link.createdAt,
        lastUpdated: link.updatedAt
      });
    } else {
      return json({ isLinked: false });
    }
    
  } catch (error) {
    console.error('OAuth2 link status error:', error);
    return json({ error: 'Failed to get link status' }, { status: 500 });
  }
};

/**
 * Unlink Office Flow account
 */
export const DELETE: RequestHandler = async ({ locals }) => {
  try {
    // Check if user is authenticated
    if (!locals.user) {
      return json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Check if user has an Office Flow link
    const existingLink = await getOfficeFlowLinkByUserId(locals.user.id);
    if (!existingLink) {
      return json({ error: 'No Office Flow account is linked' }, { status: 404 });
    }
    
    // Deactivate the link
    await deactivateOfficeFlowLink(locals.user.id);
    
    return json({ message: 'Office Flow account unlinked successfully' });
    
  } catch (error) {
    console.error('OAuth2 unlink error:', error);
    return json({ error: 'Failed to unlink Office Flow account' }, { status: 500 });
  }
};
