import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { getTasksByUserId, getTasksDueToday, getOverdueTasks, getCategoriesByUserId } from '$lib/server/db/operations.js';

export const load: LayoutServerLoad = async ({ locals }) => {
  if (!locals.user) {
    throw redirect(302, '/login');
  }

  try {
    // Load user's tasks and categories
    const [allTasks, todayTasks, overdueTasks, categories] = await Promise.all([
      getTasksByUserId(locals.user.id),
      getTasksDueToday(locals.user.id),
      getOverdueTasks(locals.user.id),
      getCategoriesByUserId(locals.user.id)
    ]);

    // Calculate metrics
    const completedTasks = allTasks.filter(task => task.completed);
    const activeTasks = allTasks.filter(task => !task.completed);

    // Get this week's tasks (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const thisWeekTasks = activeTasks.filter(task =>
      task.dueDate && new Date(task.dueDate) <= nextWeek
    );

    const completionRate = allTasks.length > 0
      ? Math.round((completedTasks.length / allTasks.length) * 100)
      : 0;

    return {
      user: locals.user,
      tasks: {
        all: allTasks,
        today: todayTasks,
        overdue: overdueTasks,
        active: activeTasks,
        completed: completedTasks,
        thisWeek: thisWeekTasks
      },
      categories,
      metrics: {
        todayCount: todayTasks.length,
        overdueCount: overdueTasks.length,
        thisWeekCount: thisWeekTasks.length,
        completionRate
      }
    };
  } catch (error) {
    console.error('Dashboard load error:', error);
    // Return empty data on error
    return {
      user: locals.user,
      tasks: {
        all: [],
        today: [],
        overdue: [],
        active: [],
        completed: [],
        thisWeek: []
      },
      categories: [],
      metrics: {
        todayCount: 0,
        overdueCount: 0,
        thisWeekCount: 0,
        completionRate: 0
      }
    };
  }
};
