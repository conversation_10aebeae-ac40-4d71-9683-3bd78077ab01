<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import type { LayoutData } from './$types';
  import ToastContainer from '$lib/components/ui/ToastContainer.svelte';
  import NavigationProgress from '$lib/components/ui/NavigationProgress.svelte';
  import NetworkStatus from '$lib/components/ui/NetworkStatus.svelte';
  import '../../app.css';

  export let data: LayoutData;
  let sidebarOpen = false;

  $: currentPath = $page.url.pathname;

  $: {
    if (typeof document !== 'undefined') {
      if (sidebarOpen) {
        document.body.classList.add('overflow-hidden');
      } else {
        document.body.classList.remove('overflow-hidden');
      }
    }
  }

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  function closeSidebar() {
    sidebarOpen = false;
  }
</script>

<svelte:head>
  <title>Dashboard - Routine Mail</title>
</svelte:head>

<!-- Navigation Progress Bar -->
<NavigationProgress />

<!-- Network Status -->
<NetworkStatus />

<!-- Toast Container -->
<ToastContainer />

<!-- Mobile-first responsive layout -->
<div class="min-h-screen bg-gray-50">
  <!-- Desktop Sidebar - Enhanced Professional Design -->
  <aside class="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:block lg:w-64 lg:bg-white lg:border-r lg:border-gray-200 lg:shadow-sm">
    <!-- Desktop Sidebar Header -->
    <div class="flex items-center px-6 py-6 border-b border-gray-200 bg-gray-50/50">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200">
        <div class="p-2 bg-blue-50 rounded-lg">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-blue-600">
            <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
          </svg>
        </div>
        <span class="tracking-tight">Routine Mail</span>
      </a>
    </div>

    <!-- Desktop Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-1">
      <a href="/dashboard/tasks"
         class="flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-50 text-blue-600 shadow-sm border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600 hover:shadow-sm'}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </div>
        <span class="font-semibold">Tasks</span>
      </a>

      <a href="/dashboard/calendar"
         class="flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/calendar') ? 'bg-blue-50 text-blue-600 shadow-sm border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600 hover:shadow-sm'}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {currentPath.startsWith('/dashboard/calendar') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <span class="font-semibold">Calendar</span>
      </a>

      <a href="/dashboard/categories"
         class="flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-50 text-blue-600 shadow-sm border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600 hover:shadow-sm'}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
        </div>
        <span class="font-semibold">Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-50 text-blue-600 shadow-sm border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600 hover:shadow-sm'}">
        <div class="flex items-center justify-center w-8 h-8 rounded-lg {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </div>
        <span class="font-semibold">Settings</span>
      </a>
    </nav>

    <!-- Desktop User Profile -->
    <div class="px-4 py-6 border-t border-gray-200 bg-gray-50/30">
      <div class="space-y-3">
        <div class="flex items-center gap-3 px-3 py-2 bg-white rounded-lg shadow-sm">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 truncate">
              {data.user.email}
            </div>
          </div>
        </div>
        <button
          class="w-full px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow"
          on:click={handleLogout}>
          <div class="flex items-center justify-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout
          </div>
        </button>
      </div>
    </div>
  </aside>

  <!-- Mobile Sidebar Overlay - only show on medium screens (md) where bottom nav is hidden -->
  <aside class="hidden md:block lg:hidden fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out {sidebarOpen ? 'translate-x-0' : '-translate-x-full'}">
    <!-- Mobile Sidebar Header -->
    <div class="flex items-center justify-between px-6 py-6 border-b border-gray-200">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-semibold text-gray-900" on:click={closeSidebar}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
        </svg>
        <span>Routine Mail</span>
      </a>
      <button
        class="p-2 -mr-2 text-gray-500 hover:text-gray-700"
        on:click={closeSidebar}
        aria-label="Close sidebar"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <nav class="flex-1 px-6 py-6 space-y-3">
      <a href="/dashboard/tasks"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/tasks') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </div>
        <span class="text-lg">Tasks</span>
      </a>

      <a href="/dashboard/calendar"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/calendar') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/calendar') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <span class="text-lg">Calendar</span>
      </a>

      <a href="/dashboard/categories"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/categories') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
        </div>
        <span class="text-lg">Categories</span>
      </a>

      <a href="/dashboard/settings"
         class="flex items-center gap-4 px-4 py-4 rounded-xl font-medium transition-all duration-200 {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-50 text-blue-600 shadow-sm' : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'}"
         on:click={closeSidebar}>
        <div class="flex items-center justify-center w-10 h-10 rounded-lg {currentPath.startsWith('/dashboard/settings') ? 'bg-blue-100' : 'bg-gray-100'}">
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </div>
        <span class="text-lg">Settings</span>
      </a>
    </nav>

    <!-- Mobile User Profile -->
    <div class="px-6 py-6 border-t border-gray-200">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 truncate">
              {data.user.email}
            </div>
            <div class="text-xs text-gray-500">
              Logged in
            </div>
          </div>
        </div>
        <button
          class="w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
          on:click={handleLogout}>
          <div class="flex items-center justify-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span>Logout</span>
          </div>
        </button>
      </div>
    </div>
  </aside>

  <!-- Mobile Header - Enhanced Professional Design -->
  <header class="lg:hidden flex items-center px-4 py-4 bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-30 shadow-sm">
    <!-- Left side: Back button (always present for consistent height) -->
    <div class="w-10 flex justify-start">
      {#if currentPath.includes('/new') || currentPath.includes('/edit') || currentPath.match(/\/tasks\/[^\/]+$/)}
        <button
          class="w-9 h-9 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all duration-200 active:scale-95"
          on:click={() => history.back()}
          aria-label="Go back"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      {:else}
        <!-- Empty spacer to maintain consistent height -->
        <div class="w-9 h-9"></div>
      {/if}
    </div>

    <!-- Centered logo with enhanced styling -->
    <div class="flex-1 flex justify-center">
      <a href="/dashboard/tasks" class="flex items-center gap-3 text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200">
        <div class="p-1.5 bg-blue-50 rounded-lg">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-blue-600">
            <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" stroke-width="2" stroke-linecap="round"></rect>
          </svg>
        </div>
        <span class="tracking-tight">Routine Mail</span>
      </a>
    </div>

    <!-- Right spacer -->
    <div class="w-10"></div>
    <!-- Sidebar toggle button - only show on medium screens (md) where bottom nav is hidden -->
    <button
      class="hidden md:block lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      on:click={() => sidebarOpen = !sidebarOpen}
      aria-label="Toggle sidebar"
    >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
    </button>
  </header>

  <!-- Main Content -->
  <div class="lg:ml-64">
    <main class="px-4 py-6 pb-24 md:pb-20 lg:px-8 lg:py-10 lg:pb-10 max-w-7xl mx-auto">
      <slot />
    </main>
  </div>

  <!-- Mobile Bottom Navigation - Unified Professional Design -->
  <nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 z-30 h-16 shadow-lg">
    <div class="flex items-center justify-around h-full px-1">
      <a href="/dashboard/tasks"
         class="flex flex-col items-center justify-center w-16 py-1.5 text-xs font-medium transition-all duration-200 relative">
        <!-- Icon Container -->
        <div class="flex items-center justify-center w-7 h-7 mb-0.5 transition-all duration-200 {currentPath.startsWith('/dashboard/tasks') ? 'text-blue-600' : 'text-gray-600'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </div>
        <!-- Label -->
        <span class="text-xs font-medium {currentPath.startsWith('/dashboard/tasks') ? 'text-blue-600' : 'text-gray-600'}">Tasks</span>
        <!-- Active Indicator -->
        {#if currentPath.startsWith('/dashboard/tasks')}
          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-blue-600 rounded-full"></div>
        {/if}
      </a>

      <a href="/dashboard/calendar"
         class="flex flex-col items-center justify-center w-16 py-1.5 text-xs font-medium transition-all duration-200 relative">
        <!-- Icon Container -->
        <div class="flex items-center justify-center w-7 h-7 mb-0.5 transition-all duration-200 {currentPath.startsWith('/dashboard/calendar') ? 'text-blue-600' : 'text-gray-600'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <!-- Label -->
        <span class="text-xs font-medium {currentPath.startsWith('/dashboard/calendar') ? 'text-blue-600' : 'text-gray-600'}">Calendar</span>
        <!-- Active Indicator -->
        {#if currentPath.startsWith('/dashboard/calendar')}
          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-blue-600 rounded-full"></div>
        {/if}
      </a>

      <a href="/dashboard/categories"
         class="flex flex-col items-center justify-center w-16 py-1.5 text-xs font-medium transition-all duration-200 relative">
        <!-- Icon Container -->
        <div class="flex items-center justify-center w-7 h-7 mb-0.5 transition-all duration-200 {currentPath.startsWith('/dashboard/categories') ? 'text-blue-600' : 'text-gray-600'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
        </div>
        <!-- Label -->
        <span class="text-xs font-medium {currentPath.startsWith('/dashboard/categories') ? 'text-blue-600' : 'text-gray-600'}">Categories</span>
        <!-- Active Indicator -->
        {#if currentPath.startsWith('/dashboard/categories')}
          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-blue-600 rounded-full"></div>
        {/if}
      </a>

      <a href="/dashboard/settings"
         class="flex flex-col items-center justify-center w-16 py-1.5 text-xs font-medium transition-all duration-200 relative">
        <!-- Icon Container -->
        <div class="flex items-center justify-center w-7 h-7 mb-0.5 transition-all duration-200 {currentPath.startsWith('/dashboard/settings') ? 'text-blue-600' : 'text-gray-600'}">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </div>
        <!-- Label -->
        <span class="text-xs font-medium {currentPath.startsWith('/dashboard/settings') ? 'text-blue-600' : 'text-gray-600'}">Settings</span>
        <!-- Active Indicator -->
        {#if currentPath.startsWith('/dashboard/settings')}
          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-blue-600 rounded-full"></div>
        {/if}
      </a>
    </div>
  </nav>

  <!-- Mobile Sidebar Overlay Background - only show on medium screens -->
  {#if sidebarOpen}
    <div
      class="hidden md:block lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50"
      on:click={closeSidebar}
      on:keydown={(e) => e.key === 'Escape' && closeSidebar()}
      role="button"
      tabindex="0"
      aria-label="Close sidebar overlay"
    ></div>
  {/if}
</div>
