<script lang="ts">
  import type { PageData } from './$types';
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import { fade, crossfade } from 'svelte/transition';
  import { cubicOut } from 'svelte/easing';

  export let data: PageData;

  let completingTasks = new Set<string>();
  
  // Create crossfade for smooth transitions between incomplete and completed lists
  const [send, receive] = crossfade({
    duration: 300,
    easing: cubicOut
  });

  // Create a compact status summary for the header
  const statusSummary = {
    overdue: data.metrics.overdueCount,
    today: data.metrics.todayCount,
    thisWeek: data.metrics.thisWeekCount
  };

  // Organize tasks by priority and urgency for better display
  const allTasks = [
    // First show overdue tasks (highest priority)
    ...data.tasks.overdue.map(task => ({ ...task, urgency: 'overdue' })),
    // Then today's tasks
    ...data.tasks.today.map(task => ({ ...task, urgency: 'today' })),
    // Then other active tasks, sorted by priority
    ...data.tasks.active
      .filter(task => {
        const todayIds = new Set(data.tasks.today.map(t => t.id));
        const overdueIds = new Set(data.tasks.overdue.map(t => t.id));
        return !todayIds.has(task.id) && !overdueIds.has(task.id);
      })
      .sort((a, b) => b.priority - a.priority)
      .map(task => ({ ...task, urgency: 'upcoming' }))
  ];

  // Create reactive variables for tasks to support optimistic updates
  let incompleteTasks = allTasks.filter(task => !task.completed).slice(0, 8);
  let completedTasks = data.tasks.completed.map(task => ({ ...task, urgency: 'completed' }));
  
  // Update tasks when data changes
  $: {
    incompleteTasks = allTasks.filter(task => !task.completed).slice(0, 8);
    completedTasks = data.tasks.completed.map(task => ({ ...task, urgency: 'completed' }));
  }

  // Get upcoming tasks for sidebar
  const upcomingTasks = data.tasks.active
    .filter(task => task.dueDate && new Date(task.dueDate) > new Date())
    .slice(0, 3)
    .map(task => ({
      date: formatRelativeDate(new Date(task.dueDate!)),
      task: task.title
    }));

  function getCurrentDate() {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date().toLocaleDateString('en-US', options);
  }

  function formatRelativeDate(date: Date): string {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      const diffTime = date.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays <= 7) {
        return date.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
    }
  }

  function formatTaskInfo(task: any): string {
    const category = data.categories.find(c => c.id === task.categoryId);
    const categoryName = category ? category.name : '';

    if (task.completed) {
      return `Completed ${task.completedAt ? new Date(task.completedAt).toLocaleDateString() : ''}`;
    }

    if (task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const timeStr = dueDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
      const dateStr = formatRelativeDate(dueDate);
      return `Due ${dateStr} at ${timeStr}${categoryName ? ' • ' + categoryName : ''}`;
    }

    return categoryName || 'No due date';
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'high';
      case 1: return 'normal';
      default: return 'low';
    }
  }

  async function completeTask(taskId: string) {
    if (completingTasks.has(taskId)) return; // Prevent double-click

    completingTasks.add(taskId);
    completingTasks = completingTasks; // Trigger reactivity

    // Find the task to complete
    const taskToComplete = incompleteTasks.find(t => t.id === taskId);
    if (!taskToComplete) return;

    // Optimistically update UI - move task from incomplete to completed
    const optimisticCompletedTask = {
      ...taskToComplete,
      completed: true,
      completedAt: new Date().toISOString(),
      urgency: 'completed' as const
    };
    
    // Update local arrays optimistically
    incompleteTasks = incompleteTasks.filter(t => t.id !== taskId);
    completedTasks = [optimisticCompletedTask, ...completedTasks];

    try {
      const response = await fetch(`/api/tasks/${taskId}/complete`, {
        method: 'POST'
      });

      if (response.ok) {
        // Success - invalidate to get fresh data from server
        await invalidateAll();
      } else {
        // Revert optimistic update on failure
        console.error('Failed to complete task');
        completedTasks = completedTasks.filter(t => t.id !== taskId);
        incompleteTasks = [...incompleteTasks, taskToComplete].slice(0, 8);
      }
    } catch (error) {
      console.error('Error completing task:', error);
      // Revert optimistic update on error
      completedTasks = completedTasks.filter(t => t.id !== taskId);
      incompleteTasks = [...incompleteTasks, taskToComplete].slice(0, 8);
    } finally {
      completingTasks.delete(taskId);
      completingTasks = completingTasks; // Trigger reactivity
    }
  }

  async function deleteTask(taskId: string) {
    if (!confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        await invalidateAll();
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  }
</script>

<div class="page-header">
  <div class="header-main">
    <div class="header-text">
      <h1 class="page-title">Your Tasks</h1>
      <p class="page-subtitle">{getCurrentDate()}</p>
    </div>
    <div class="header-actions">
      <a href="/dashboard/tasks/new" class="btn-primary">+ New Task</a>
      <a href="/dashboard/tasks" class="btn-secondary">View All</a>
    </div>
  </div>

  <!-- Compact status bar -->
  {#if statusSummary.overdue > 0 || statusSummary.today > 0}
    <div class="status-bar">
      {#if statusSummary.overdue > 0}
        <div class="status-item urgent">
          <span class="status-icon">⚠️</span>
          <span class="status-text">{statusSummary.overdue} overdue</span>
        </div>
      {/if}
      {#if statusSummary.today > 0}
        <div class="status-item today">
          <span class="status-icon">📅</span>
          <span class="status-text">{statusSummary.today} due today</span>
        </div>
      {/if}
      {#if statusSummary.thisWeek > 0}
        <div class="status-item week">
          <span class="status-icon">📋</span>
          <span class="status-text">{statusSummary.thisWeek} this week</span>
        </div>
      {/if}
    </div>
  {/if}
</div>

<div class="dashboard-content">
  <!-- Task List Section -->
  <div class="tasks-container">
    {#if incompleteTasks.length === 0 && completedTasks.length === 0}
       <div class="empty-state">
         <div class="empty-icon">
           <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 13a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-6z"></path><path d="M18 9V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v3"></path><path d="M12 11v6"></path><path d="M12 3a3 3 0 0 0-3 3v2"></path><path d="M15 6V5a3 3 0 0 0-3-3"></path></svg>
         </div>
         <h3>All caught up!</h3>
         <p>You have no pending tasks. Great job!</p>
         <a href="/dashboard/tasks/new" class="btn-primary">Create your first task</a>
       </div>
     {:else}
      <div class="task-list">
        {#each incompleteTasks as task (task.id)}
          <div key={task.id} class="task-row" class:completed={task.completed} in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
            <div class="task-cell-main">
              <input
                type="checkbox"
                class="task-checkbox"
                checked={task.completed}
                on:change|stopPropagation={() => !task.completed && completeTask(task.id)}
                disabled={task.completed || completingTasks.has(task.id)}
              />
              <a href="/dashboard/tasks/{task.id}" class="task-title-link" on:click|stopPropagation>
                <span class="task-title" class:completed={task.completed}>{task.title}</span>
              </a>
            </div>
            <div class="task-cell-meta">
              {#if task.urgency === 'overdue'}
                <span class="urgency-badge overdue">Overdue</span>
              {:else if task.urgency === 'today'}
                <span class="urgency-badge today">Today</span>
              {/if}
              <span class="task-due">{formatTaskInfo(task)}</span>
               <span class="priority-badge priority-{getPriorityLabel(task.priority)}">
                {getPriorityLabel(task.priority)}
              </span>
            </div>
            <div class="task-actions">
               <button
                  class="action-btn"
                  title="Edit Task"
                  on:click|stopPropagation={() => goto(`/dashboard/tasks/${task.id}/edit`)}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/></svg>
                </button>
                <button
                  class="action-btn delete"
                  title="Delete Task"
                  on:click|stopPropagation={() => deleteTask(task.id)}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m-6 4v6m4-6v6"/></svg>
                </button>
            </div>
          </div>
        {/each}
      </div>
    {/if}

    {#if completedTasks.length > 0}
      <div class="completed-section">
        <details open>
          <summary>
            Completed ({completedTasks.length})
            <div class="divider"></div>
          </summary>
          <div class="task-list">
            {#each completedTasks as task (task.id)}
              <div key={task.id} class="task-row completed" in:receive|local={{key: task.id}} out:send|local={{key: task.id}}>
                <div class="task-cell-main">
                  <input
                    type="checkbox"
                    class="task-checkbox"
                    checked={true}
                    disabled
                  />
                  <a href="/dashboard/tasks/{task.id}" class="task-title-link" on:click|stopPropagation>
                     <span class="task-title completed">{task.title}</span>
                  </a>
                </div>
                <div class="task-cell-meta">
                  <span class="task-due">{formatTaskInfo(task)}</span>
                </div>
                <div class="task-actions">
                  <button
                    class="action-btn delete"
                    title="Delete Task"
                    on:click|stopPropagation={() => deleteTask(task.id)}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m-6 4v6m4-6v6"/></svg>
                  </button>
                </div>
              </div>
            {/each}
          </div>
        </details>
      </div>
    {/if}
  </div>
</div>

<style>
  /* Page Header */
  .page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
  }
  .header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .page-title {
    font-size: 1.5rem; /* Reduced size for a cleaner look */
    font-weight: 600;
    color: #212529;
  }
  .page-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
  }
  .header-actions {
    display: flex;
    gap: 0.5rem;
  }
  .btn-primary, .btn-secondary {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    border: 1px solid transparent;
    cursor: pointer;
  }
  .btn-primary {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
  .btn-primary:hover {
    background-color: #0056b3;
  }
  .btn-secondary {
    background-color: #ffffff;
    color: #495057;
    border: 1px solid #ced4da;
  }
  .btn-secondary:hover {
    background-color: #f8f9fa;
  }

  /* Status Bar */
  .status-bar {
    display: flex;
    gap: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-top: 1rem;
  }
  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
  }
   .status-icon {
     /* Using inline SVGs is better, but for now we remove the distracting emoji */
  }
  .status-item.urgent { color: #dc3545; }
  .status-item.today { color: #007bff; }
  .status-item.week { color: #28a745; }


  /* Main Content Layout */
  .dashboard-content {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
  }

  /* Task List */
  .task-list {
    display: flex;
    flex-direction: column;
  }
  .task-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
  }
  .task-row:last-child {
    border-bottom: none;
  }
  .task-row:hover {
    background-color: #f8f9fa;
  }
  .task-row.completed {
    opacity: 0.7;
  }
  .task-row.completed .task-title {
    text-decoration: line-through;
    color: #6c757d;
  }

  .completed-section {
    margin-top: 1.5rem;
  }

  .completed-section summary {
    font-weight: 600;
    font-size: 1rem;
    color: #343a40;
    cursor: pointer;
    padding: 0.75rem 1.25rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .completed-section summary::marker {
    content: '';
  }

  .completed-section summary:before {
    content: '▶';
    font-size: 0.8em;
    transition: transform 0.2s;
  }

  .completed-section details[open] > summary:before {
    transform: rotate(90deg);
  }
  .task-cell-main {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-grow: 1;
  }
  .task-checkbox {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
  .task-title-link {
    text-decoration: none;
    color: #212529;
  }
  .task-title {
    font-size: 0.95rem;
    font-weight: 500;
  }
  .task-title-link:hover .task-title {
    color: #007bff;
  }
  .task-cell-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
    transition: opacity 0.2s ease-in-out;
  }

  .task-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    opacity: 0; /* Hidden by default */
    transition: opacity 0.2s ease-in-out;
  }

  .task-row:hover .task-actions {
    opacity: 1; /* Show on hover */
  }
  
  .task-row:hover .task-cell-meta {
    opacity: 0; /* Hide meta on hover to show actions */
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.35rem;
    color: #6c757d;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .action-btn:hover {
    color: #212529;
    background-color: #e9ecef;
  }

  .action-btn.delete:hover {
    color: #dc3545;
    background-color: #f8d7da;
  }

  /* Badges */
  .urgency-badge, .priority-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
  }
  .urgency-badge.overdue {
    background-color: #fde8e8;
    color: #c53030;
  }
  .urgency-badge.today {
    background-color: #e6f2ff;
    color: #007bff;
  }
  .priority-badge {
    background-color: #e9ecef;
    color: #495057;
  }
  .priority-badge.priority-high {
    background-color: #fde8e8;
    color: #c53030;
  }
  .priority-badge.priority-normal {
    background-color: #fff4e6;
    color: #dd6b20;
  }
  .priority-badge.priority-low {
    background-color: #e6f2ff;
    color: #007bff;
  }

  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: #6c757d;
  }
  .empty-icon {
    color: #adb5bd;
    margin-bottom: 1rem;
  }
  .empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
  }
  .empty-state p {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }
  
  .completed-section .task-row {
    border-color: #f1f3f5;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .task-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    .task-cell-meta {
      width: 100%;
      justify-content: flex-start;
      padding-left: 2.5rem; /* Align with title */
    }
  }



</style>
