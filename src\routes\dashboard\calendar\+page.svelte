<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import { Calendar } from '@fullcalendar/core';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import listPlugin from '@fullcalendar/list';
  import interactionPlugin from '@fullcalendar/interaction';
  import type { PageData } from './$types';
  import PageTransition from '$lib/components/ui/PageTransition.svelte';
  import Button from '$lib/components/ui/Button.svelte';

  export let data: PageData;

  let calendarEl: HTMLElement;
  let calendar: Calendar;
  let currentView = 'dayGridMonth';

  // Convert tasks to FullCalendar events format
  $: events = data.tasks
    .filter(task => task.dueDate) // Only include tasks with due dates
    .map(task => ({
      id: task.id,
      title: task.title,
      start: task.dueDate,
      allDay: true,
      backgroundColor: task.priority === 3 ? '#ef4444' :
                      task.priority === 2 ? '#f59e0b' : '#22c55e',
      borderColor: task.priority === 3 ? '#dc2626' :
                  task.priority === 2 ? '#d97706' : '#16a34a',
      extendedProps: {
        description: task.notes,
        priority: task.priority,
        categoryId: task.categoryId,
        completed: task.completed
      }
    }));

  onMount(() => {
    if (calendarEl) {
      calendar = new Calendar(calendarEl, {
        plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
        initialView: 'dayGridMonth',
        headerToolbar: false, // 禁用内部工具栏
        height: 'auto',
        events: events,
        eventClick: function(info) {
          // Navigate to task detail page using SvelteKit navigation
          goto(`/dashboard/tasks/${info.event.id}`);
        },
        dateClick: function(info) {
          // Navigate to create new task with selected date using SvelteKit navigation
          const date = info.dateStr;
          goto(`/dashboard/tasks/new?date=${date}`);
        },
        eventDidMount: function(info) {
          // Add tooltip with task description
          if (info.event.extendedProps.description) {
            info.el.title = info.event.extendedProps.description;
          }
          
          // Add completed styling
          if (info.event.extendedProps.completed) {
            info.el.style.opacity = '0.6';
            info.el.style.textDecoration = 'line-through';
          }
        },
        viewDidMount: function(info) {
          currentView = info.view.type;
        }
      });

      calendar.render();
    }
  });

  onDestroy(() => {
    if (calendar) {
      calendar.destroy();
    }
  });

  // Update events when data changes
  $: if (calendar && events) {
    calendar.removeAllEvents();
    calendar.addEventSource(events);
  }

  // Update calendar when view changes
  $: if (calendar && currentView) {
    calendar.changeView(currentView);
  }

  function handleViewChange(view: string) {
    currentView = view;
    if (calendar) {
      calendar.changeView(view);
    }
  }

  function goToToday() {
    if (calendar) {
      calendar.today();
    }
  }

  function goToPrev() {
    if (calendar) {
      calendar.prev();
    }
  }

  function goToNext() {
    if (calendar) {
      calendar.next();
    }
  }
</script>

<svelte:head>
  <title>Calendar - Routine Mail</title>
</svelte:head>

<PageTransition>
  <div class="calendar-page">
    <!-- Mobile Header -->
    <div class="lg:hidden mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-4">Calendar</h1>
      
      <!-- Mobile Controls -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-2">
          <Button variant="ghost" size="sm" on:click={goToPrev}>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </Button>
          <Button variant="ghost" size="sm" on:click={goToNext}>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </Button>
          <Button variant="secondary" size="sm" on:click={goToToday}>Today</Button>
        </div>
        
        <!-- Mobile View Selector -->
        <select
          class="form-input text-sm py-1 px-2"
          value={currentView}
          on:change={(e) => handleViewChange((e.target as HTMLSelectElement).value)}
        >
          <option value="dayGridMonth">Month</option>
          <option value="timeGridWeek">Week</option>
          <option value="timeGridDay">Day</option>
          <option value="listWeek">List</option>
        </select>
      </div>
    </div>

    <!-- Desktop Header -->
    <div class="hidden lg:block mb-6">
      <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold text-gray-900">Calendar</h1>
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <Button variant="ghost" on:click={goToPrev}>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </Button>
            <Button variant="ghost" on:click={goToNext}>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </Button>
            <Button variant="secondary" on:click={goToToday}>Today</Button>
          </div>
          
          <!-- Desktop View Buttons -->
          <div class="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            <button 
              class="px-3 py-1 text-sm rounded-md transition-colors {currentView === 'dayGridMonth' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              on:click={() => handleViewChange('dayGridMonth')}
            >
              Month
            </button>
            <button 
              class="px-3 py-1 text-sm rounded-md transition-colors {currentView === 'timeGridWeek' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              on:click={() => handleViewChange('timeGridWeek')}
            >
              Week
            </button>
            <button 
              class="px-3 py-1 text-sm rounded-md transition-colors {currentView === 'timeGridDay' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              on:click={() => handleViewChange('timeGridDay')}
            >
              Day
            </button>
            <button 
              class="px-3 py-1 text-sm rounded-md transition-colors {currentView === 'listWeek' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              on:click={() => handleViewChange('listWeek')}
            >
              List
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Container -->
    <div class="calendar-container card">
      <div bind:this={calendarEl} class="calendar-wrapper"></div>
    </div>

    <!-- Legend -->
    <div class="mt-6 flex flex-wrap items-center gap-4 text-sm text-gray-600">
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-red-500 rounded"></div>
        <span>High Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-yellow-500 rounded"></div>
        <span>Medium Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-green-500 rounded"></div>
        <span>Low Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-gray-400 rounded opacity-60"></div>
        <span>Completed</span>
      </div>
    </div>
  </div>
</PageTransition>

<style>
  .calendar-page {
    @apply max-w-7xl mx-auto;
  }

  .calendar-container {
    @apply p-6;
  }

  .calendar-wrapper {
    @apply min-h-[600px];
  }

  /* FullCalendar custom styles */
  :global(.fc) {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  :global(.fc-event) {
    @apply cursor-pointer border-l-4 rounded-md;
  }

  :global(.fc-event:hover) {
    @apply shadow-md;
  }

  :global(.fc-daygrid-event) {
    @apply text-xs font-medium;
  }

  :global(.fc-day-today) {
    @apply bg-blue-50;
  }

  :global(.fc-day-past) {
    @apply bg-gray-50;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    :global(.fc-toolbar) {
      @apply flex-col gap-2;
    }
    
    :global(.fc-toolbar-chunk) {
      @apply flex justify-center;
    }
    
    :global(.fc-daygrid-event) {
      @apply text-xs;
    }
  }
</style>
