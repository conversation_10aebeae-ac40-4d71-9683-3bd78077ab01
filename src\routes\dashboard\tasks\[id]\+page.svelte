<script lang="ts">
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import type { PageData } from './$types';
  import Button from '$lib/components/ui/Button.svelte';
  import TaskActionMenu from '$lib/components/ui/TaskActionMenu.svelte';

  export let data: PageData;

  // Local bindings for reactive data
  let task: any;
  let categories: any[] = [];
  let category: any;


  $: ({ task, categories } = data);

  // Find category for this task
  $: category = task && categories ? categories.find(c => c.id === task.categoryId) : undefined;

  // Normalize subtasks into objects with completion state
  type SubtaskItem = { text: string; completed: boolean };
  let localSubtasks: SubtaskItem[] = [];
  $: if (task) {
    const raw = Array.isArray(task.subtasks) ? task.subtasks : [];
    localSubtasks = raw.map((s: any) =>
      typeof s === 'string' ? { text: s, completed: false } : { text: s?.text ?? '', completed: !!s?.completed }
    );
  }

  let localCompleted = task ? task.completed : false;
  let toggling = false;

  import { relativeLabel } from '$lib/utils/datetime';
  function formatDate(date: string | null): string {
    const tz = data?.user?.timezone || 'Asia/Kuala_Lumpur';
    return relativeLabel(date, tz);
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'High';
      case 1: return 'Normal';
      default: return 'Low';
    }
  }

  function getPriorityColor(priority: number): string {
    switch (priority) {
      case 2: return '#dc2626';
      case 1: return '#059669';
      default: return '#64748b';
    }
  }

  // Keep localCompleted in sync with server data when not actively toggling
  $: if (task && !toggling) {
    localCompleted = !!task.completed;
  }

  async function toggleComplete(event: Event) {
    if (toggling) return;
    const input = event.currentTarget as HTMLInputElement;
    const nextChecked = input?.checked ?? !localCompleted;

    try {
      toggling = true;
      // Optimistic UI update
      localCompleted = nextChecked;

      const completing = nextChecked === true;
      const endpoint = completing ? `/api/tasks/${task.id}/complete` : `/api/tasks/${task.id}`;
      const method = completing ? 'POST' : 'PUT';
      const body = completing ? undefined : JSON.stringify({ completed: false, completedAt: null });

      const response = await fetch(endpoint, {
        method,
        headers: completing ? {} : { 'Content-Type': 'application/json' },
        body
      });

      if (response.ok) {
        await invalidateAll();
      } else {
        // Revert optimistic update on failure
        localCompleted = !nextChecked;


        console.error('Failed to toggle task completion');
      }
    } catch (error) {
      // Revert optimistic update on error
      localCompleted = !nextChecked;
      console.error('Error toggling task completion:', error);
    } finally {
      toggling = false;
    }
  }

  async function deleteTask() {
    if (!confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        goto('/dashboard/tasks');
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  }

  function handleEdit() {
    goto(`/dashboard/tasks/${task.id}/edit`);
  }

  function handleDelete() {
    deleteTask();
  }

  async function toggleSubtask(index: number) {
    const next = !localSubtasks[index].completed;
    localSubtasks = localSubtasks.map((s, i) => i === index ? { ...s, completed: next } : s);
    try {
      const payload = { subtasks: localSubtasks.map(s => ({ text: s.text, completed: s.completed })) };
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload)
      });
      if (!response.ok) throw new Error('Failed to update subtask');
    } catch (e) {
      localSubtasks = localSubtasks.map((s, i) => i === index ? { ...s, completed: !next } : s);
      console.error(e);
    }
  }
</script>

<svelte:head>
  <title>{task.title} - Routine Mail</title>
</svelte:head>

<div class="task-detail-container">
  <!-- Desktop Header -->
  <div class="header-actions flex hidden md:flex">
    <a href="/dashboard/tasks" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap bg-black text-white border border-black hover:bg-gray-800 focus:ring-blue-500 shadow-sm hover:shadow-md back-to-tasks-btn gap-2">Back</a>
    <div class="flex gap-2 ml-auto">
      <Button href="/dashboard/tasks/{task.id}/edit" variant="primary">Edit Task</Button>
      <Button variant="danger" on:click={deleteTask}>Delete</Button>
    </div>
  </div>

  <!-- Mobile Header - Only Action Menu -->
  <div class="mobile-header md:hidden flex justify-end mb-4">
    <TaskActionMenu
      taskId={task.id}
      taskTitle={task.title}
      on:edit={handleEdit}
      on:delete={handleDelete}
    />
  </div>
  <div class="task-card">
    <div class="task-header">
      <div class="task-status">
        <input


          type="checkbox"
          class="task-check"
          checked={localCompleted}
          on:change={toggleComplete}
          disabled={toggling}
        />
        <h1 class="task-title" class:completed={localCompleted}>{task.title}</h1>
      </div>

      <div class="task-meta">
        <div class="priority-badge" style="--priority-color: {getPriorityColor(task.priority)}">
          <div class="priority-indicator"></div>
          {getPriorityLabel(task.priority)} Priority
        </div>

        {#if category}
          <div class="category-badge">
            <div class="category-color" style="background: {category.color}"></div>
            {category.name}
          </div>
        {/if}
      </div>
    </div>

    <div class="task-info">
      <div class="info-item">
        <span class="info-label">Due Date:</span>
        <span class="info-value" class:overdue={task.dueDate && new Date(task.dueDate) < new Date() && !task.completed}>
          {formatDate(task.dueDate)}
        </span>
      </div>

      <div class="info-item">
        <span class="info-label">Created:</span>
        <span class="info-value">{new Date(task.createdAt).toLocaleDateString()}</span>
      </div>

      {#if task.completed && task.completedAt}
        <div class="info-item">
          <span class="info-label">Completed:</span>
          <span class="info-value">{new Date(task.completedAt).toLocaleDateString()}</span>
        </div>
      {/if}
    </div>

    {#if task.notes}
      <div class="task-notes">
        <h3>Notes</h3>
        <p>{task.notes}</p>
      </div>
    {/if}

    {#if localSubtasks && localSubtasks.length > 0}
      <div class="task-subtasks">
        <h3>Subtasks ({localSubtasks.length})</h3>
        <ul class="subtasks-list">
          {#each localSubtasks as subtask, i}
            <li class="subtask-item">
              <input type="checkbox" class="subtask-check" checked={subtask.completed} on:change={() => toggleSubtask(i)} />
              <span class:line-through={subtask.completed}>{subtask.text}</span>
            </li>
          {/each}
        </ul>
      </div>
    {/if}

    {#if task.recurrenceRule}
      <div class="task-recurrence">
        <h3>Recurrence</h3>
        <p>This task repeats {task.recurrenceRule.type === 'daily' ? 'daily' :
           task.recurrenceRule.type === 'weekly' ? 'weekly' :
           task.recurrenceRule.type === 'monthly' ? 'monthly' :
           task.recurrenceRule.type === 'yearly' ? 'yearly' :
           task.recurrenceRule.type}</p>
      </div>
    {/if}
  </div>


</div>

<style>
  .header-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-bottom: 1rem;
  }

  .back-to-tasks-btn {
    margin-right: auto;
  }

  /* Mobile Header Styles */
  .mobile-header {
    @apply px-1 py-2;
  }

  .task-detail-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .task-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  }

  .task-header {
    margin-bottom: 2rem;
  }

  .task-status {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .task-check {
    width: 24px;
    height: 24px;
    border: 2px solid #cbd5e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 0.25rem;
  }

  .task-check:hover {
    border-color: #4299e1;
  }

  .task-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
    line-height: 1.2;
  }

  /* Section containers for clarity */
  .section-card {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
  }

  .task-title.completed {
    text-decoration: line-through;
    color: #718096;
  }

  .task-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .priority-badge, .category-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .subtask-check {
    width: 18px;
    height: 18px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

  .priority-badge {
    background: rgba(66, 153, 225, 0.1);
    color: var(--priority-color);
    border: 1px solid rgba(66, 153, 225, 0.2);
  }

  .priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  .category-badge {
    background: #f8fafc;
    color: #374151;
    border: 1px solid #e2e8f0;
  }

  .category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .task-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
  }
  @media (max-width: 768px) {
    .task-info { grid-template-columns: 1fr; }
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .info-label {
    font-weight: 600;
    color: #374151;
  }

  .info-value {
    color: #6b7280;
  }

  .info-value.overdue {
    color: #dc2626;
    font-weight: 600;
  }

  .task-notes, .task-subtasks, .task-recurrence {
    margin-bottom: 2rem;
  }

  .task-notes h3, .task-subtasks h3, .task-recurrence h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }

  .task-notes p {
    color: #374151;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .subtasks-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .subtask-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 0.75rem;
    color: #374151;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
  }


  .subtask-check {
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
    background: white;
  }

  .subtask-check:hover {
    border-color: #4299e1;
  }

  .subtask-check:checked {
    background-color: #4299e1;
    border-color: #4299e1;
  }

  .subtask-item span {
    line-height: 1.4;
  }


  .subtask-bullet {
    color: #4299e1;
    font-weight: bold;
    margin-top: 0.125rem;
  }

  @media (max-width: 768px) {
    .page-header {
      padding: 0 1rem;
      margin-bottom: 1rem;
    }

    .header-actions {
      width: 100%;
    }

    .header-actions .btn-secondary,
    .header-actions .btn-danger {
      flex: 1;
      text-align: center;
      justify-content: center;
      padding: 0.625rem 0.75rem;
      font-size: 0.875rem;
    }

    .task-card {
      padding: 1.5rem;
      margin: 0.5rem;
      border-radius: 16px;
    }

    .task-title {
      font-size: 1.5rem;
    }

    .task-meta {
      flex-direction: column;
      align-items: flex-start;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }
  }
</style>
