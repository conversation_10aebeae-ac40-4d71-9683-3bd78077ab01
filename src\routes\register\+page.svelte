<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';

  let email = '';
  let password = '';
  let confirmPassword = '';
  let otpCode = '';
  let loading = false;
  let error = '';
  let success = '';
  let requestId = '';
  let step: 'email' | 'otp' = 'email';
  let remainingTime = 0;
  let cooldownTimer: NodeJS.Timeout | null = null;

  // Office Flow integration
  let showOfficeFlowModal = false;
  let pendingOfficeFlowState = '';
  let officeFlowUserInfo: any = null;

  function startCooldownTimer(seconds: number) {
    remainingTime = seconds;
    cooldownTimer = setInterval(() => {
      remainingTime--;
      if (remainingTime <= 0) {
        clearInterval(cooldownTimer!);
        cooldownTimer = null;
      }
    }, 1000);
  }

  onMount(() => {
    // Check for pending Office Flow registration
    const urlParams = new URLSearchParams($page.url.search);
    const pendingState = urlParams.get('pending_office_flow');

    if (pendingState) {
      pendingOfficeFlowState = pendingState;
      showOfficeFlowModal = true;

      // Try to get Office Flow user info from the pending state
      // This is just for display purposes - the actual linking happens after registration
      console.log('Detected pending Office Flow registration with state:', pendingState);
    }
  });

  async function handleEmailSubmit() {
    if (!email) {
      error = 'Please enter your email';
      return;
    }

    if (password !== confirmPassword) {
      error = 'Passwords do not match';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        requestId = data.requestId;
        step = 'otp';
        success = 'Verification code sent to your email';
      } else {
        error = data.error || 'Registration failed';
        if (data.remainingTime) {
          startCooldownTimer(data.remainingTime);
        }
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function handleOtpSubmit() {
    if (!otpCode || !password) {
      error = 'Please fill in all fields';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ requestId, code: otpCode, password }),
      });

      const data = await response.json();

      if (response.ok) {
        success = 'Registration successful!';

        // If there's a pending Office Flow state, complete the linking
        if (pendingOfficeFlowState) {
          await completeOfficeFlowLinking(data.user.id);
        } else {
          setTimeout(() => goto('/dashboard'), 1500);
        }
      } else {
        error = data.error || 'Verification failed';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  function goBackToEmail() {
    step = 'email';
    otpCode = '';
    error = '';
    success = '';
  }

  async function completeOfficeFlowLinking(userId: string) {
    try {
      const response = await fetch('/api/auth/complete-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pendingState: pendingOfficeFlowState,
          userId
        }),
      });

      const data = await response.json();

      if (response.ok) {
        success = `Registration successful! Your Office Flow account (${data.officeFlowUser.email}) has been linked. Redirecting...`;
        setTimeout(() => goto('/dashboard'), 2000);
      } else {
        error = data.error || 'Failed to link Office Flow account';
        // Still redirect to dashboard even if linking fails
        setTimeout(() => goto('/dashboard'), 2000);
      }
    } catch (err) {
      console.error('Failed to complete Office Flow linking:', err);
      error = 'Registration successful, but failed to link Office Flow account. You can link it manually in settings.';
      setTimeout(() => goto('/dashboard'), 2000);
    }
  }

  function closeOfficeFlowModal() {
    showOfficeFlowModal = false;
  }
</script>

<svelte:head>
  <title>Register - Routine Mail</title>
</svelte:head>

<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">Routine Mail</div>
      <h1>{step === 'email' ? 'Create Account' : 'Verify Email'}</h1>
      <p>{step === 'email' ? 'Sign up for a new account' : 'Enter the verification code sent to your email'}</p>
    </div>

    {#if step === 'email'}
      <form on:submit|preventDefault={handleEmailSubmit} class="auth-form">
        {#if error}
          <div class="error-message">
            {error}
            {#if remainingTime > 0}
              <br>Please wait {remainingTime} seconds before trying again.
            {/if}
          </div>
        {/if}

        {#if success}
          <div class="success-message">{success}</div>
        {/if}

        <div class="form-group">
          <label for="email">Email</label>
          <input
            id="email"
            type="email"
            bind:value={email}
            placeholder="Enter your email"
            required
            disabled={loading}
          />
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input
            id="password"
            type="password"
            bind:value={password}
            placeholder="8-16 characters with upper and lowercase"
            required
            disabled={loading}
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input
            id="confirmPassword"
            type="password"
            bind:value={confirmPassword}
            placeholder="Confirm your password"
            required
            disabled={loading}
          />
        </div>

        <button type="submit" class="auth-btn" disabled={loading || remainingTime > 0}>
          {loading ? 'Sending...' : remainingTime > 0 ? `Wait ${remainingTime}s` : 'Send Verification Code'}
        </button>
      </form>
    {:else}
      <form on:submit|preventDefault={handleOtpSubmit} class="auth-form">
        {#if error}
          <div class="error-message">{error}</div>
        {/if}

        {#if success}
          <div class="success-message">{success}</div>
        {/if}

        <div class="form-group">
          <label for="otpCode">Verification Code</label>
          <input
            id="otpCode"
            type="text"
            bind:value={otpCode}
            placeholder="Enter 6-digit code"
            maxlength="6"
            required
            disabled={loading}
          />
          <small>Code sent to {email}</small>
        </div>

        <button type="submit" class="auth-btn" disabled={loading}>
          {loading ? 'Verifying...' : 'Verify & Create Account'}
        </button>

        <button type="button" class="auth-btn secondary" on:click={goBackToEmail} disabled={loading}>
          Back to Email
        </button>
      </form>
    {/if}

    <div class="auth-footer">
      <p>Already have an account? <a href="/login">Sign in</a></p>
    </div>
  </div>
</div>

<!-- Office Flow Modal -->
{#if showOfficeFlowModal}
  <div class="modal-overlay" on:click={closeOfficeFlowModal}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h3>Office Flow Account Detected</h3>
        <button class="close-btn" on:click={closeOfficeFlowModal}>×</button>
      </div>
      <div class="modal-body">
        <div class="office-flow-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          </svg>
        </div>
        <p><strong>Your Office Flow account is not linked to any Routine Mail account.</strong></p>
        <p>To use "Sign in with Office Flow", you need to:</p>
        <ol>
          <li>Complete the registration process below</li>
          <li>Your Office Flow account will be automatically linked</li>
          <li>Next time, you can sign in directly with Office Flow</li>
        </ol>
        <div class="modal-note">
          <p><strong>Note:</strong> After registration, you'll be able to sign in using either your email/password or Office Flow.</p>
        </div>
      </div>
      <div class="modal-actions">
        <button class="btn-primary" on:click={closeOfficeFlowModal}>
          Continue Registration
        </button>
        <a href="/login" class="btn-secondary">
          I Have an Account
        </a>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Reuse the same styles as login page */
  :global(body) {
    font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  .auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 3rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .auth-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2d3748;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }

  .logo::after {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #4299e1, #63b3ed);
    border-radius: 50%;
    margin-left: 0.5rem;
    vertical-align: middle;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0 0 0.5rem 0;
  }

  .auth-header p {
    color: #718096;
    margin: 0;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.875rem;
  }

  input {
    padding: 0.875rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
  }

  input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  input:disabled {
    background: #f7fafc;
    cursor: not-allowed;
  }

  small {
    color: #718096;
    font-size: 0.75rem;
  }

  .auth-btn {
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .auth-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .auth-btn.secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .auth-btn.secondary:hover:not(:disabled) {
    background: rgba(237, 242, 247, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    text-align: center;
  }

  .success-message {
    background: #f0fff4;
    color: #38a169;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    text-align: center;
  }

  .auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
  }

  .auth-footer p {
    color: #718096;
    margin: 0;
  }

  .auth-footer a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
  }

  .auth-footer a:hover {
    text-decoration: underline;
  }

  /* Office Flow Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    animation: modalSlideIn 0.3s ease-out;
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
  }

  .close-btn:hover {
    background: #f7fafc;
    color: #2d3748;
  }

  .modal-body {
    padding: 1.5rem;
    text-align: center;
  }

  .office-flow-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    color: #4299e1;
  }

  .office-flow-icon svg {
    width: 100%;
    height: 100%;
  }

  .modal-body p {
    margin: 0 0 1rem 0;
    color: #4a5568;
    line-height: 1.6;
  }

  .modal-body ol {
    text-align: left;
    margin: 1rem 0;
    padding-left: 1.5rem;
    color: #4a5568;
  }

  .modal-body li {
    margin: 0.5rem 0;
    line-height: 1.5;
  }

  .modal-note {
    background: #f0fff4;
    border: 1px solid #c6f6d5;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .modal-note p {
    margin: 0;
    color: #22543d;
    font-size: 0.875rem;
  }

  .modal-actions {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    justify-content: flex-end;
  }

  .modal-actions .btn-primary,
  .modal-actions .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .modal-actions .btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .modal-actions .btn-primary:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .modal-actions .btn-secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .modal-actions .btn-secondary:hover {
    background: rgba(237, 242, 247, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
</style>
